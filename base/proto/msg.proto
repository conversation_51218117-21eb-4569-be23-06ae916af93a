syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

import "enum.proto";

import "google/protobuf/any.proto";

//After are enums.
//After are structs.
//After are messages.
message S2C_ErrorResultMessage {
  int32 code = 1; //
}
message C2S_RegisterMessage {
  string username = 1; //用户名
  string password = 2; //密码
  string platform = 3; //平台
  string ver = 4; //客户端当前版本
  string distanceId = 5; //ta访客id
  string os = 6; //系统
}
message S2C_RegisterResultMessage {
  int32 code = 1; //注册结果，0成功，1用户名存在， 2用户名或密码不规范
  string id = 2; //用户id
  string token = 3; //免密token
}
message C2S_LoginAccountMessage {
  string username = 1; //用户名,游客不需要携带
  string password = 2; //密码,游客不需要携带
  LoginCommon common = 3; //公共参数
}
message C2S_LoginByTokenMessage {
  string id = 1; //用户id
  string token = 2; //免密登录
  bool isReconnect = 3; //是不是重连登陆请求
  LoginCommon common = 4; //公共参数
}
message C2S_LoginGuestMessage {
  LoginCommon common = 1; //公共参数
  string userId = 2; //游客 id
}
message C2S_LoginFBMessage {
  LoginCommon common = 1; //公共参数
  string userId = 2; //facebook id
  string token = 3; //token
}
message C2S_BindFBMessage {
  LoginCommon common = 1; //公共参数
  string userId = 2; //facebook id
  string token = 3; //token
}
message C2S_LoginAppleMessage {
  LoginCommon common = 1; //公共参数
  string userId = 2; //用户id
  string nickName = 3; //用户名字
  string token = 4; //token
  string code = 5; //code
}
message C2S_BindAppleMessage {
  LoginCommon common = 1; //公共参数
  string userId = 2; //用户id
  string nickName = 3; //用户名字
  string token = 4; //token
  string code = 5; //code
}
message C2S_LoginGoogleMessage {
  LoginCommon common = 1; //公共参数
  string token = 2; //token
  string nickName = 3; //用户名字
  string avatarUrl = 4; //用户头像
}
message C2S_BindGoogleMessage {
  LoginCommon common = 1; //公共参数
  string token = 2; //token
  string nickName = 3; //用户名字
  string avatarUrl = 4; //用户头像
}
message C2S_LoginWxMessage {
  LoginCommon common = 1; //公共参数
  string code = 2; //用户id
  string nickName = 3; //用户名字
  string avatarUrl = 4; //用户头像
}
message C2S_LoginWxAppMessage {
  LoginCommon common = 1; //公共参数
  string code = 2; //用户id
}
message C2S_LoginTapTapMessage {
  LoginCommon common = 1; //公共参数
  string userId = 2; //用户id
  string nickName = 3; //用户名字
  string kid = 4; //kid
  string mac_key = 5; //mac_key
}
message C2S_CertificationMessage {
  string realName = 1; //真实姓名
  string idCard = 2; //身份证号
}
message S2C_CertificationResultMessage {
  int32 code = 1; //验证结果（0：成功，1：身份证姓名不匹配）
  int32 age = 2; //年龄
}
message S2C_LoginResultMessage {
  int32 code = 1; //登录结果（0：登录成功，此时userInfo是有效数据，1：用户名或密码错误，2：免密登录token失效，需要重新登录），3：客户端版本需要更新
  UserInfo userInfo = 2; //用户数据信息
}
message S2C_BindResultMessage {
  int32 code = 1; //0
  string userType = 2; //用户类型
  string nickName = 3; //昵称
  string avatarUrl = 4; //头像
}
message S2C_NoticeMessage {
  string content = 1; //测试通知客户端
}
message C2S_EnterGameServerMessage {
  int32 sid = 1; //服务器区号
  string token = 2; //登录服获取到的token
  bool closeGuide = 3; //是否关闭新手教程
}
message S2C_EnterGameServerResultMessage {
  int32 code = 1; //选区结果,前端没做选区,游戏服启动的情况下,暂时都是成功
}
message C2S_GetPlayerInfoMessage {
}
message S2C_GetPlayerInfoResMessage {
  int32 code = 1; //
  Player player = 2; //玩家基础数据
}
message S2C_CurrencyChangeMessage {
  CurrencyInfo currency = 1; //货币实体
}
message S2C_BagItemChangeMessage {
  repeated ItemInfo items = 1; //道具列表
}
message C2S_GmExecuteMessage {
  string cmd = 1; //gm命令
}
message S2C_GmExecuteRspMessage {
  string reply = 1; //
}
message C2S_JackpotReqMessage {
  string msgId = 1; //消息号
  int32 type = 2; //1单抽2十连抽
}
message S2C_JackpotRspMessage {
  int32 code = 1; //code=0代表无错误
  repeated Condition list = 2; //抽卡结果列表
}
message C2S_CollectItemMessage {
  repeated Output carriages = 1; //车厢产出
  int32 time = 2; //加速时间
  int32 passengerStar = 3; //乘客星尘
  int32 heart = 4; //爱心
}
message S2C_CollectItemRespMessage {
  int32 code = 1; //code=0代表无错误
  int32 passengerStar = 2; //乘客星尘
  int32 heart = 3; //爱心
  repeated Output carriages = 4; //车厢产出
}
message C2S_SpeedUpMessage {
}
message S2C_SpeedUpMessage {
  int32 code = 1; //code=0代表无错误, 1表示玩家未处于加速中
  fixed64 time = 2; //游戏模拟时间
  int32 energy = 3; //剩余体力
}
message C2S_StopSpeedUpMessage {
}
message S2C_StopSpeedUpMessage {
  int32 code = 1; //code=0代表无错误, 1表示玩家未处于加速中
  fixed64 time = 2; //游戏模拟时间
  int32 energy = 3; //剩余体力
}
message C2S_SyncSpeedUpMessage {
}
message S2C_SyncSpeedUpMessage {
  int32 code = 1; //code=0代表无错误, 1表示玩家未处于加速中
  fixed64 time = 2; //游戏模拟时间
  int32 energy = 3; //剩余体力
}
message C2S_RecoverEnergyMessage {
  int32 type = 1; //1免费2扣钻石
}
message S2C_RecoverEnergyRespMessage {
  int32 code = 1; //0成功
  Energy energy = 2; //探索数据
}
message C2S_SyncMessage {
}
message S2C_SyncMessage {
  fixed64 time = 1; //游戏时长
  double energy = 2; //剩余体力
  int32 electricTime = 3; //电力加速剩余时间
}
message S2C_LogoutMessage {
  int32 reason = 1; //0角色在其他地方登录
}
message C2S_RecordGuideStepMessage {
  int32 guideId = 1; //当前引导模块id
  int32 stepId = 2; //当前步骤id
}
message S2C_RecordGuideStepResultMessage {
  int32 code = 1; //0成功1数据不对2步骤id不对
}
message C2S_ClaimTaskRewardMessage {
  string id = 1; //任务id
}
message S2C_ClaimTaskRewardResultMessage {
  int32 code = 1; //0成功
}
message C2S_SyncPlanetMessage {
}
message S2C_SyncPlanetMessage {
  int32 code = 1; //0成功
  Planet planet = 2; //当前星球数据
}
message C2S_SyncDailyInfoMessage {
}
message S2C_SyncDailyInfoRespMessage {
  int32 code = 1; //
  int32 jackpotDailyNum = 2; //每日抽卡次数
  Wanted wanted = 3; //悬赏
  Energy energy = 4; //加速能量
  BlackHole blackHole = 5; //黑洞
  Instance instance = 6; //每日副本数据
  Store store = 7; //商店数据
  int32 nextDaySurpluTime = 8; //下次每日刷新剩余时间
  int32 nextWeekSurplusTime = 9; //下次周刷新时间
  ArrestModule arrestData = 10; //通缉令数据
  DailyTaskInfo dailyTask = 11; //每日任务数据
  Transport transport = 12; //运送任务
  SpaceStone spaceStone = 13; //空间宝石
  int32 offlineRewardTime = 14; //离线奖励时长
}
message C2S_MailDetailMessage {
  string mailId = 1; //邮件id
}
message S2C_MailDetailRespMessage {
  int32 code = 1; //非0则有错
  MailInfo mail = 2; //邮件详情
}
message S2C_OnNewMailMessage {
  MailInfo mail = 1; //邮件信息
}
message C2S_DeleteReadMailMessage {
}
message S2C_DeleteReadMailRespMessage {
  int32 code = 1; //无需判断
}
message C2S_AttachMailMessage {
  string mailId = 1; //-1代表全部
}
message S2C_AttachMailRespMessage {
  int32 code = 1; //0成功
  repeated Condition rewards = 2; //奖励列表,针对全部领取
  repeated string ids = 3; //需要更新的邮件列表
}
message C2S_MailListMessage {
  int32 type = 1; //0是获取邮件列表1是获取红点状态
}
message S2C_MailListRespMessage {
  repeated MailInfo mail = 1; //邮件信息列表
}
message C2S_CheckCdkMessage {
  string cdk = 1; //兑换码
}
message S2C_CheckCdkMessage {
  int32 code = 1; //非0则失败
  repeated Condition rewards = 2; //奖励列表
}
message C2S_DiyPlayerInfoMessage {
  string nickName = 1; //玩家名称
}
message S2C_DiyPlayerInfoRespMessage {
  int32 code = 1; //非0则失败
}
message C2S_SignOutMessage {
  string authField1 = 1; //认证信息1
  string authField2 = 2; //认证信息2
}
message S2C_SignOutRespMessage {
  int32 code = 1; //1游客不能注销2认证信息匹配失败
}
message C2S_CancelSignOutMessage {
}
message S2C_CancelSignOutRespMessage {
  int32 code = 1; //非0失败,1账号信息不匹配,2账号未处于注销,3账号已经走完注销流程,无法恢复
}
message C2S_RemoveNewMarkMessage {
  int32 typeId = 1; //new标签类型
  repeated int32 aryVal = 2; //new标签数据
}
message S2C_RemoveNewMarkMessage {
  int32 code = 1; //0成功
}
message C2S_BuyBatteryMessage {
  int32 num = 1; //买多少电池
}
message S2C_BuyBatteryMessage {
  int32 code = 1; //
}
message C2S_SetBattleTeamMessage {
  BattleTeam team = 1; //编队
}
message S2C_SetBattleTeamMessage {
  int32 code = 1; //
}
message C2S_JackpotPointsGetMessage {
}
message S2C_JackpotPointsGetMessage {
  int32 code = 1; //非0积分不足
  repeated Condition list = 2; //结果列表
}
message C2S_UnlockSpeedUpAutoMessage {
}
message S2C_UnlockSpeedUpAutoMessage {
  int32 code = 1; //非0消耗不足
}
message C2S_GetAdRewardMessage {
  AdType type = 1; //类型
}
message S2C_GetAdRewardMessage {
  int32 code = 1; //
  google.protobuf.Any data = 2; //返回的数据
}
message S2C_OnGetBurstTaskMessage {
  BurstTaskItem data = 1; //
}
