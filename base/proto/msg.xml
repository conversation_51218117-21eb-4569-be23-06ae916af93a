<?xml version="1.0" encoding="UTF-8" ?>
<messages package="./pb">
    <message type="S2C" name="ErrorResult" explain="通用返回消息">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="Register" module="login" explain="客户端发起注册,返回一个token走免密登录">
        <field class="string" name="username" explain="用户名"/>
        <field class="string" name="password" explain="密码"/>
        <field class="string" name="platform" explain="平台"/>
        <field class="string" name="ver" explain="客户端当前版本"/>
        <field class="string" name="distanceId" explain="ta访客id"/>
        <field class="string" name="os" explain="系统"/>
    </message>
    <message type="S2C" name="RegisterResult" explain="注册结果">
        <field class="int32" name="code" explain="注册结果，0成功，1用户名存在， 2用户名或密码不规范"/>
        <field class="string" name="id" explain="用户id"/>
        <field class="string" name="token" explain="免密token"/>
    </message>
    <message type="C2S" name="LoginAccount" module="login" explain="用户使用账号密码登录" resp="LoginResult">
        <field class="string" name="username" explain="用户名,游客不需要携带"/>
        <field class="string" name="password" explain="密码,游客不需要携带"/>
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
    </message>
    <message type="C2S" name="LoginByToken" module="login" explain="用户使用免密token登录" resp="LoginResult">
        <field class="string" name="id" explain="用户id"/>
        <field class="string" name="token" explain="免密登录"/>
        <field class="bool" name="isReconnect" explain="是不是重连登陆请求"/>
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
    </message>
    <message type="C2S" name="LoginGuest" module="login" explain="游客登录,返回免密token登录" resp="LoginResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="userId" explain="游客 id"/>
    </message>
    <message type="C2S" name="LoginFB" module="login" explain="fb登录,返回免密token登录" resp="LoginResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="userId" explain="facebook id"/>
        <field class="string" name="token" explain="token"/>
    </message>
    <message type="C2S" name="BindFB" module="login" explain="绑定fb账号,返回免密token登录" resp="BindResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="userId" explain="facebook id"/>
        <field class="string" name="token" explain="token"/>
    </message>
    <message type="C2S" name="LoginApple" module="login" explain="苹果登录,返回免密token登录" resp="LoginResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="userId" explain="用户id"/>
        <field class="string" name="nickName" explain="用户名字"/>
        <field class="string" name="token" explain="token"/>
        <field class="string" name="code" explain="code"/>
    </message>
    <message type="C2S" name="BindApple" module="login" explain="绑定苹果,返回免密token登录" resp="BindResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="userId" explain="用户id"/>
        <field class="string" name="nickName" explain="用户名字"/>
        <field class="string" name="token" explain="token"/>
        <field class="string" name="code" explain="code"/>
    </message>
    <message type="C2S" name="LoginGoogle" module="login" explain="谷歌登录,返回免密token登录" resp="LoginResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="token" explain="token"/>
        <field class="string" name="nickName" explain="用户名字"/>
        <field class="string" name="avatarUrl" explain="用户头像"/>
    </message>
    <message type="C2S" name="BindGoogle" module="login" explain="绑定谷歌账号,返回免密token登录" resp="BindResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="token" explain="token"/>
        <field class="string" name="nickName" explain="用户名字"/>
        <field class="string" name="avatarUrl" explain="用户头像"/>
    </message>
    <message type="C2S" name="LoginWx" module="login" explain="微信小程序登录,返回免密token登录" resp="LoginResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="code" explain="用户id"/>
        <field class="string" name="nickName" explain="用户名字"/>
        <field class="string" name="avatarUrl" explain="用户头像"/>
    </message>
    <message type="C2S" name="LoginWxApp" module="login" explain="微信App登录,返回免密token登录" resp="LoginResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="code" explain="用户id"/>
    </message>
    <message type="C2S" name="LoginTapTap" module="login" explain="苹果登录,返回免密token登录" resp="LoginResult">
        <field class="struct.LoginCommon" name="common" explain="公共参数"/>
        <field class="string" name="userId" explain="用户id"/>
        <field class="string" name="nickName" explain="用户名字"/>
        <field class="string" name="kid" explain="kid"/>
        <field class="string" name="mac_key" explain="mac_key"/>
    </message>
    <message type="C2S" name="Certification" module="login" explain="实名认证">
        <field class="string" name="realName" explain="真实姓名"/>
        <field class="string" name="idCard" explain="身份证号"/>
    </message>
    <message type="S2C" name="CertificationResult" explain="实名认证结果">
        <field class="int32" name="code" explain="验证结果（0：成功，1：身份证姓名不匹配）"/>
        <field class="int32" name="age" explain="年龄"/>
    </message>
    <message type="S2C" name="LoginResult" explain="登录结果">
        <field class="int32" name="code" explain="登录结果（0：登录成功，此时userInfo是有效数据，1：用户名或密码错误，2：免密登录token失效，需要重新登录），3：客户端版本需要更新"/>
        <field class="struct.UserInfo" name="userInfo" explain="用户数据信息"/>
    </message>
    <message type="S2C" name="BindResult" explain="绑定结果">
        <field class="int32" name="code" explain="0"/>
        <field class="string" name="userType" explain="用户类型"/>
        <field class="string" name="nickName" explain="昵称"/>
        <field class="string" name="avatarUrl" explain="头像"/>
    </message>
    <message type="S2C" name="Notice" explain="测试消息">
        <field class="string" name="content" explain="测试通知客户端"/>
    </message>
    <message type="C2S" name="EnterGameServer" module="login" explain="选区">
        <field class="int32" name="sid" explain="服务器区号"/>
        <field class="string" name="token" explain="登录服获取到的token"/>
        <field class="bool" name="closeGuide" explain="是否关闭新手教程"/>
    </message>
    <message type="S2C" name="EnterGameServerResult" explain="选区结果">
        <field class="int32" name="code" explain="选区结果,前端没做选区,游戏服启动的情况下,暂时都是成功"/>
    </message>
    <message type="C2S" name="GetPlayerInfo" module="game" explain="获取玩家基础数据" opt="log=false">
    </message>
    <message type="S2C" name="GetPlayerInfoRes" explain="玩家基础数据消息返回">
        <field class="int32" name="code" explain=""/>
        <field class="struct.Player" name="player" explain="玩家基础数据"/>
    </message>
    <message type="S2C" name="CurrencyChange" explain="货币变化消息">
        <field class="struct.CurrencyInfo" name="currency" explain="货币实体"/>
    </message>
    <message type="S2C" name="BagItemChange" explain="背包道具变化消息">
        <array class="struct.ItemInfo" name="items" explain="道具列表"/>
    </message>
    <message type="C2S" name="GmExecute" module="game" explain="执行Gm命令">
        <field class="string" name="cmd" explain="gm命令"/>
    </message>
    <message type="S2C" name="GmExecuteRsp" explain="执行Gm命令返回(可有可无)">
        <field class="string" name="reply" explain=""/>
    </message>
    <message type="C2S" name="JackpotReq" module="game" explain="抽卡消息" resp="JackpotRsp">
        <field class="string" name="msgId" explain="消息号"/>
        <field class="int32" name="type" explain="1单抽2十连抽"/>
    </message>
    <message type="S2C" name="JackpotRsp" explain="抽卡结果">
        <field class="int32" name="code" explain="code=0代表无错误"/>
        <array class="struct.Condition" name="list" explain="抽卡结果列表"/>
    </message>
    <message type="C2S" name="CollectItem" module="game" explain="收取资源">
        <array class="struct.Output" name="carriages" explain="车厢产出"/>
        <field class="int32" name="time" explain="加速时间"/>
        <field class="int32" name="passengerStar" explain="乘客星尘"/>
        <field class="int32" name="heart" explain="爱心"/>
    </message>
    <message type="S2C" name="CollectItemResp" explain="收取资源">
        <field class="int32" name="code" explain="code=0代表无错误"/>
        <field class="int32" name="passengerStar" explain="乘客星尘"/>
        <field class="int32" name="heart" explain="爱心"/>
        <array class="struct.Output" name="carriages" explain="车厢产出"/>
    </message>
    <message type="C2S" name="SpeedUp" module="game" explain="开始使用加速">
    </message>
    <message type="S2C" name="SpeedUp" explain="开始使用加速">
        <field class="int32" name="code" explain="code=0代表无错误, 1表示玩家未处于加速中"/>
        <field class="fixed64" name="time" explain="游戏模拟时间"/>
        <field class="int32" name="energy" explain="剩余体力"/>
    </message>
    <message type="C2S" name="StopSpeedUp" module="game" explain="停止使用加速">
    </message>
    <message type="S2C" name="StopSpeedUp" explain="停止使用加速">
        <field class="int32" name="code" explain="code=0代表无错误, 1表示玩家未处于加速中"/>
        <field class="fixed64" name="time" explain="游戏模拟时间"/>
        <field class="int32" name="energy" explain="剩余体力"/>
    </message>
    <message type="C2S" name="SyncSpeedUp" module="game" explain="同步加速状态">
    </message>
    <message type="S2C" name="SyncSpeedUp" explain="同步加速状态">
        <field class="int32" name="code" explain="code=0代表无错误, 1表示玩家未处于加速中"/>
        <field class="fixed64" name="time" explain="游戏模拟时间"/>
        <field class="int32" name="energy" explain="剩余体力"/>
    </message>
    <message type="C2S" name="RecoverEnergy" module="game" explain="恢复加速能量">
        <field class="int32" name="type" explain="1免费2扣钻石"/>
    </message>
    <message type="S2C" name="RecoverEnergyResp" explain="恢复加速能量">
        <field class="int32" name="code" explain="0成功"/>
        <field class="struct.Energy" name="energy" explain="探索数据"/>
    </message>
    <message type="C2S" name="Sync" module="game" explain="与客户端同步部分数值">
    </message>
    <message type="S2C" name="Sync" explain="与客户端同步部分数值">
        <field class="fixed64" name="time" explain="游戏时长"/>
        <field class="double" name="energy" explain="剩余体力"/>
        <field class="int32" name="electricTime" explain="电力加速剩余时间"/>
    </message>
    <message type="S2C" name="Logout" explain="通知客户端登出,这个消息发送完毕后session会立即关闭">
        <field class="int32" name="reason" explain="0角色在其他地方登录"/>
    </message>
    <message type="C2S" name="RecordGuideStep" module="game" explain="记录引导数据关键步骤">
        <field class="int32" name="guideId" explain="当前引导模块id"/>
        <field class="int32" name="stepId" explain="当前步骤id"/>
    </message>
    <message type="S2C" name="RecordGuideStepResult" explain="记录引导数据结果">
        <field class="int32" name="code" explain="0成功1数据不对2步骤id不对"/>
    </message>
    <message type="C2S" name="ClaimTaskReward" module="game" explain="领取任务奖励">
        <field class="string" name="id" explain="任务id"/>
    </message>
    <message type="S2C" name="ClaimTaskRewardResult" explain="领取任务奖励结果">
        <field class="int32" name="code" explain="0成功"/>
    </message>
    <message type="C2S" name="SyncPlanet" module="game" explain="同步当前星球数据">
    </message>
    <message type="S2C" name="SyncPlanet" explain="同步当前星球数据">
        <field class="int32" name="code" explain="0成功"/>
        <field class="struct.Planet" name="planet" explain="当前星球数据"/>
    </message>
    <message type="C2S" name="SyncDailyInfo" module="game" explain="同步每日刷新的数据">
    </message>
    <message type="S2C" name="SyncDailyInfoResp" explain="同步每日刷新的数据">
        <field class="int32" name="code" explain=""/>
        <field class="int32" name="jackpotDailyNum" explain="每日抽卡次数"/>
        <field class="struct.Wanted" name="wanted" explain="悬赏"/>
        <field class="struct.Energy" name="energy" explain="加速能量"/>
        <field class="struct.BlackHole" name="blackHole" explain="黑洞"/>
        <field class="struct.Instance" name="instance" explain="每日副本数据"/>
        <field class="struct.Store" name="store" explain="商店数据"/>
        <field class="int32" name="nextDaySurpluTime" explain="下次每日刷新剩余时间"/>
        <field class="int32" name="nextWeekSurplusTime" explain="下次周刷新时间"/>
        <field class="struct.ArrestModule" name="arrestData" explain="通缉令数据"/>
        <field class="struct.DailyTaskInfo" name="dailyTask" explain="每日任务数据"/>
        <field class="struct.Transport" name="transport" explain="运送任务"/>
        <field class="struct.SpaceStone" name="spaceStone" explain="空间宝石"/>
        <field class="int32" name="offlineRewardTime" explain="离线奖励时长"/>
    </message>
    <message type="C2S" name="MailDetail" module="game" explain="获取邮件详情">
        <field class="string" name="mailId" explain="邮件id"/>
    </message>
    <message type="S2C" name="MailDetailResp" explain="获取邮件详情结果">
        <field class="int32" name="code" explain="非0则有错"/>
        <field class="struct.MailInfo" name="mail" explain="邮件详情"/>
    </message>
    <message type="S2C" name="OnNewMail" explain="新邮件提示">
        <field class="struct.MailInfo" name="mail" explain="邮件信息"/>
    </message>
    <message type="C2S" name="DeleteReadMail" module="game" explain="删除已读">
    </message>
    <message type="S2C" name="DeleteReadMailResp" explain="删除已读结果">
        <field class="int32" name="code" explain="无需判断"/>
    </message>
    <message type="C2S" name="AttachMail" module="game" explain="领取邮件附件">
        <field class="string" name="mailId" explain="-1代表全部"/>
    </message>
    <message type="S2C" name="AttachMailResp" explain="领取邮件附件结果">
        <field class="int32" name="code" explain="0成功"/>
        <array class="Condition" name="rewards" explain="奖励列表,针对全部领取"/>
        <array class="string" name="ids" explain="需要更新的邮件列表"/>
    </message>
    <message type="C2S" name="MailList" module="game" explain="拉取邮件列表">
        <field class="int32" name="type" explain="0是获取邮件列表1是获取红点状态"/>
    </message>
    <message type="S2C" name="MailListResp" explain="拉取邮件列表结果">
        <array class="struct.MailInfo" name="mail" explain="邮件信息列表"/>
    </message>
    <message type="C2S" name="CheckCdk" module="game" explain="兑换码">
        <field class="string" name="cdk" explain="兑换码"/>
    </message>
    <message type="S2C" name="CheckCdk" explain="兑换码兑换结果">
        <field class="int32" name="code" explain="非0则失败"/>
        <array class="struct.Condition" name="rewards" explain="奖励列表"/>
    </message>
    <message type="C2S" name="DiyPlayerInfo" module="game" explain="修改个人信息">
        <field class="string" name="nickName" explain="玩家名称"/>
    </message>
    <message type="S2C" name="DiyPlayerInfoResp" explain="修改个人信息结果">
        <field class="int32" name="code" explain="非0则失败"/>
    </message>
    <message type="C2S" name="SignOut" module="game" explain="申请注销">
        <field class="string" name="authField1" explain="认证信息1"/>
        <field class="string" name="authField2" explain="认证信息2"/>
    </message>
    <message type="S2C" name="SignOutResp" explain="申请注销">
        <field class="int32" name="code" explain="1游客不能注销2认证信息匹配失败"/>
    </message>
    <message type="C2S" name="CancelSignOut" module="login" explain="取消注销">
    </message>
    <message type="S2C" name="CancelSignOutResp" explain="取消注销结果">
        <field class="int32" name="code" explain="非0失败,1账号信息不匹配,2账号未处于注销,3账号已经走完注销流程,无法恢复"/>
    </message>
    <message type="C2S" name="RemoveNewMark" module="game" explain="去除new标签">
        <field class="int32" name="typeId" explain="new标签类型"/>
        <array class="int32" name="aryVal" explain="new标签数据"/>
    </message>
    <message type="S2C" name="RemoveNewMark" explain="去除new标签">
        <field class="int32" name="code" explain="0成功"/>
    </message>
    <message type="C2S" name="BuyBattery" module="game" explain="钻石兑换电池">
        <field class="int32" name="num" explain="买多少电池"/>
    </message>
    <message type="S2C" name="BuyBattery" explain="钻石兑换电池">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="SetBattleTeam" module="game" explain="设置战斗编队">
        <field class="struct.BattleTeam" name="team" explain="编队"/>
    </message>
    <message type="S2C" name="SetBattleTeam" explain="设置战斗编队">
        <field class="int32" name="code" explain=""/>
    </message>
    <message type="C2S" name="JackpotPointsGet" module="game" explain="抽卡积分领取">
    </message>
    <message type="S2C" name="JackpotPointsGet" explain="抽卡积分领取结果">
        <field class="int32" name="code" explain="非0积分不足"/>
        <array class="struct.Condition" name="list" explain="结果列表"/>
    </message>
    <message type="C2S" name="UnlockSpeedUpAuto" module="game" explain="解锁自动加速">
    </message>
    <message type="S2C" name="UnlockSpeedUpAuto" explain="解锁自动加速结果">
        <field class="int32" name="code" explain="非0消耗不足"/>
    </message>
    <message type="C2S" name="GetAdReward" module="game" explain="获取广告奖励">
        <field class="enum.AdType" name="type" explain="类型"/>
    </message>
    <message type="S2C" name="GetAdReward" explain="获取广告奖励">
        <field class="int32" name="code" explain=""/>
        <field class="any" name="data" explain="返回的数据"/>
    </message>
    <message type="S2C" name="OnGetBurstTask" explain="突发任务触发">
        <field class="struct.BurstTaskItem" name="data" explain=""/>
    </message>
</messages>