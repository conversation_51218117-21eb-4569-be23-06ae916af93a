package game

import (
	"train/base/structs"
	"train/common/pb"

	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitPaymentHD 自动生成，不要在这个方法添加任何内容。
func InitPaymentHD(this *Game) {
	// 创建订单
	this.middleware.Wrap("C2S_CreatePayOrderMessage", this.C2sCreatePayOrderMessageHandler)
	// 验证订单
	this.middleware.Wrap("C2S_VerifyPayOrderMessage", this.C2sVerifyPayOrderMessageHandler)
}
func (this *Game) C2sCreatePayOrderMessageHandler(player *structs.Player, msg *pb.C2S_CreatePayOrderMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	productId := msg.GetProductId()
	orderPlat := msg.GetOrderPlatform()
	code, options := player.Pay.CreateOrder(productId, orderPlat)
	return &pb.S2C_CreatePayOrderMessage{Code: code, Options: options}
	//@action-code-end
}

func (this *Game) C2sVerifyPayOrderMessageHandler(player *structs.Player, msg *pb.C2S_VerifyPayOrderMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	orderId := msg.GetOrderId()
	pay := player.Pay
	payOrder, _ := lo.Find(pay.NotFinishOrders, func(order *structs.PayOrder) bool { return order.OrderId == orderId })
	if payOrder == nil {
		// 订单不存在
		return &pb.S2C_VerifyPayOrderMessage{Code: 1}
	}
	pay.CheckOrder(payOrder)
	// 返回订单状态
	return &pb.S2C_VerifyPayOrderMessage{State: payOrder.State}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间
//@logic-code-end
